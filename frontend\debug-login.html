<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login - RaoufStore</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 Login Debug Tool</h1>
        
        <div class="debug-container">
            <h3>Quick Admin Login Test</h3>
            <button onclick="testAdminLogin()">Test Admin Login</button>
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <button onclick="clearStorage()">Clear Storage</button>
            <div id="test-results"></div>
        </div>

        <div class="debug-container">
            <h3>Manual Login Test</h3>
            <div>
                <input type="email" id="email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password" value="Admin@123">
                <button onclick="manualLogin()">Login</button>
            </div>
            <div id="manual-results"></div>
        </div>

        <div class="debug-container">
            <h3>Current Storage</h3>
            <button onclick="showStorage()">Refresh Storage</button>
            <pre id="storage-content"></pre>
        </div>

        <div class="debug-container">
            <h3>Console Logs</h3>
            <pre id="console-logs"></pre>
        </div>
    </div>

    <script>
        // Auto-detect API URL
        const API_URL = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost' 
            ? 'http://localhost:3000/api' 
            : 'http://localhost:3000/api';

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        let logs = [];

        console.log = function(...args) {
            logs.push(`[LOG] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
            originalError.apply(console, args);
        };

        function updateConsoleLogs() {
            document.getElementById('console-logs').textContent = logs.slice(-10).join('\n');
        }

        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
        }

        async function testBackendHealth() {
            try {
                console.log('Testing backend health...');
                const response = await fetch(`${API_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('test-results', `✅ Backend healthy: ${data.message}`, 'success');
                    console.log('Backend health check passed:', data);
                } else {
                    addResult('test-results', `❌ Backend unhealthy: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('test-results', `❌ Backend connection failed: ${error.message}`, 'error');
                console.error('Backend health check failed:', error);
            }
        }

        async function testAdminLogin() {
            try {
                console.log('Testing admin login...');
                addResult('test-results', '🔐 Testing admin login...', 'info');

                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin@123'
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                const data = await response.json();
                console.log('Login response:', data);
                addResult('test-results', `📡 Response: ${JSON.stringify(data)}`, 'info');

                if (response.ok && data.success) {
                    addResult('test-results', `✅ Admin login successful! Role: ${data.user.role}`, 'success');

                    // Store the data with detailed logging
                    console.log('💾 Storing authentication data...');
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.user));
                    localStorage.setItem('userRole', data.user.role);
                    localStorage.setItem('user', JSON.stringify(data.user)); // Backward compatibility

                    console.log('✅ Data stored successfully');
                    addResult('test-results', '💾 Authentication data stored', 'success');

                    // Verify storage
                    const storedToken = localStorage.getItem('token');
                    const storedUserInfo = localStorage.getItem('userInfo');
                    console.log('🔍 Verification - Token stored:', !!storedToken);
                    console.log('🔍 Verification - UserInfo stored:', !!storedUserInfo);

                    showStorage();

                    // Test redirect
                    if (data.user.role === 'admin') {
                        addResult('test-results', `🎯 Would redirect to: admin/admin-dashboard.html`, 'info');

                        // Ask if user wants to redirect
                        if (confirm('Login successful! Redirect to admin dashboard?')) {
                            window.location.href = 'admin/admin-dashboard.html';
                        }
                    }
                } else {
                    addResult('test-results', `❌ Login failed: ${data.message || 'Unknown error'}`, 'error');
                    console.error('Login failed:', data);
                }
            } catch (error) {
                addResult('test-results', `❌ Login error: ${error.message}`, 'error');
                console.error('Login test failed:', error);
            }
        }

        async function manualLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                console.log(`Manual login attempt: ${email}`);
                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                console.log('Manual login response:', data);

                if (response.ok && data.success) {
                    addResult('manual-results', `✅ Login successful! User: ${data.user.first_name} ${data.user.last_name}, Role: ${data.user.role}`, 'success');
                    
                    // Store the data
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.user));
                    localStorage.setItem('userRole', data.user.role);
                    
                    showStorage();
                } else {
                    addResult('manual-results', `❌ Login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                addResult('manual-results', `❌ Login error: ${error.message}`, 'error');
                console.error('Manual login failed:', error);
            }
        }

        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            addResult('test-results', '🧹 Storage cleared', 'info');
            showStorage();
        }

        function showStorage() {
            const storage = {
                localStorage: {},
                sessionStorage: {}
            };

            // Get localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage.localStorage[key] = localStorage.getItem(key);
            }

            // Get sessionStorage
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                storage.sessionStorage[key] = sessionStorage.getItem(key);
            }

            document.getElementById('storage-content').textContent = JSON.stringify(storage, null, 2);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
            console.log('API URL:', API_URL);
            showStorage();
            testBackendHealth();
        });
    </script>
</body>
</html>
