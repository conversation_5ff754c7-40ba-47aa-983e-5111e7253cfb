const jwt = require('jsonwebtoken');

// Secret key for JWT should be in environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'ecommerce_secret_key';

/**
 * Middleware for JWT authentication
 */
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (authHeader) {
    const token = authHeader.split(' ')[1]; // Bearer TOKEN_VALUE

    jwt.verify(token, JWT_SECRET, (err, user) => {
      if (err) {
        return res.status(403).json({ 
          success: false, 
          message: 'Token is not valid' 
        });
      }

      req.user = user;
      next();
    });
  } else {
    res.status(401).json({ 
      success: false, 
      message: 'Authorization token required' 
    });
  }
};

/**
 * Middleware to check if user is admin
 */
const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ 
      success: false, 
      message: 'Access denied: Admin privileges required' 
    });
  }
};

module.exports = {
  authenticateJWT,
  isAdmin
}; 