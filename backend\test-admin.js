// Test script to verify admin user exists and can login
const bcrypt = require('bcrypt');
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'ecommerce_phones',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

async function testAdminUser() {
  try {
    console.log('🔍 Testing admin user...');
    
    // Check if admin user exists
    const result = await pool.query(
      'SELECT user_id, username, email, password_hash, first_name, last_name, role FROM users WHERE email = $1',
      ['<EMAIL>']
    );

    if (result.rows.length === 0) {
      console.log('❌ Admin user not found in database');
      console.log('💡 Run the seed data script: psql -U postgres -d ecommerce_phones -f init-db/02-seed-data.sql');
      return;
    }

    const user = result.rows[0];
    console.log('✅ Admin user found:');
    console.log('   ID:', user.user_id);
    console.log('   Username:', user.username);
    console.log('   Email:', user.email);
    console.log('   Name:', user.first_name, user.last_name);
    console.log('   Role:', user.role);
    console.log('   Password Hash:', user.password_hash.substring(0, 20) + '...');

    // Test password
    const testPassword = 'Admin@123';
    const isValidPassword = await bcrypt.compare(testPassword, user.password_hash);
    
    if (isValidPassword) {
      console.log('✅ Password "Admin@123" is correct');
    } else {
      console.log('❌ Password "Admin@123" is incorrect');
      
      // Try to create a new hash for the correct password
      const newHash = await bcrypt.hash(testPassword, 10);
      console.log('💡 New hash for "Admin@123":', newHash);
      console.log('💡 Update query:');
      console.log(`   UPDATE users SET password_hash = '${newHash}' WHERE email = '<EMAIL>';`);
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.log('💡 Make sure PostgreSQL is running and database exists');
    console.log('💡 Check your .env file for correct database credentials');
  } finally {
    await pool.end();
  }
}

// Run the test
testAdminUser();
