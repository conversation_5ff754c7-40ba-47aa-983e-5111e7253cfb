const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// Serve static files from frontend directory
app.use(express.static(path.join(__dirname, 'frontend')));

// Handle all routes by serving the appropriate HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'index.html'));
});

app.get('/products', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'products.html'));
});

app.get('/product-details', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'product-details.html'));
});

app.get('/cart', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'cart.html'));
});

app.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'login.html'));
});

app.get('/about', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'about.html'));
});

app.get('/contact', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'contact.html'));
});

// Admin routes
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'admin', 'index.html'));
});

app.get('/admin/', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend', 'admin', 'index.html'));
});

app.get('/admin/*', (req, res) => {
  const adminFile = req.params[0];
  const filePath = path.join(__dirname, 'frontend', 'admin', adminFile);

  // Check if file exists, otherwise serve index.html for SPA routing
  const fs = require('fs');
  if (fs.existsSync(filePath)) {
    res.sendFile(filePath);
  } else {
    res.sendFile(path.join(__dirname, 'frontend', 'admin', 'index.html'));
  }
});

app.listen(PORT, () => {
  console.log(`🌐 Frontend server running on http://localhost:${PORT}`);
  console.log('📁 Serving files from ./frontend directory');
  console.log('🔗 Admin panel: http://localhost:8080/admin');
});
