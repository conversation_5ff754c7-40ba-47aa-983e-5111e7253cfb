<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Users Buttons - RaoufStore</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Users Buttons Test</h1>
        
        <div class="test-section">
            <h3>Test Sample Users Table</h3>
            <div class="data-table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="test-users-table">
                        <!-- Test users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="test-section">
            <h3>Debug Information</h3>
            <div id="debug-info" class="debug-info">
                Debug information will appear here...
            </div>
        </div>

        <!-- User Details Modal -->
        <div class="modal" id="user-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>User Details</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="modal-user-info">
                        User information will appear here...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test data
        const testUsers = [
            {
                user_id: 1,
                username: 'admin',
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin',
                created_at: '2024-01-01'
            },
            {
                user_id: 2,
                username: 'johndoe',
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                role: 'customer',
                created_at: '2024-02-15'
            },
            {
                user_id: 3,
                username: 'janedoe',
                email: '<EMAIL>',
                first_name: 'Jane',
                last_name: 'Doe',
                role: 'customer',
                created_at: '2024-03-01'
            }
        ];

        // Debug function
        function addDebug(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            console.log(message);
        }

        // Render test users
        function renderTestUsers() {
            const tableBody = document.getElementById('test-users-table');
            tableBody.innerHTML = '';

            testUsers.forEach(user => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${user.user_id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>${user.first_name} ${user.last_name}</td>
                    <td>
                        <span class="status-badge ${user.role === 'admin' ? 'status-delivered' : 'status-processing'}">
                            ${user.role}
                        </span>
                    </td>
                    <td>
                        <button class="btn small-btn edit-btn" data-id="${user.user_id}" type="button">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn small-btn delete-btn" data-id="${user.user_id}" type="button">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </td>
                `;

                tableBody.appendChild(tr);
            });

            addDebug(`✅ Rendered ${testUsers.length} test users`);
        }

        // Handle button clicks
        function handleButtonClick(e) {
            const button = e.target.closest('button');
            if (!button) return;

            const userId = button.getAttribute('data-id');
            addDebug(`🖱️ Button clicked: ${button.className} for user ID: ${userId}`);

            if (button.classList.contains('edit-btn')) {
                e.preventDefault();
                showUserDetails(parseInt(userId));
            } else if (button.classList.contains('delete-btn')) {
                e.preventDefault();
                deleteUser(parseInt(userId));
            }
        }

        // Show user details
        function showUserDetails(userId) {
            addDebug(`👁️ Showing details for user ID: ${userId}`);
            
            const user = testUsers.find(u => u.user_id === userId);
            if (!user) {
                addDebug(`❌ User not found with ID: ${userId}`);
                alert('User not found!');
                return;
            }

            const modal = document.getElementById('user-modal');
            const modalInfo = document.getElementById('modal-user-info');
            
            modalInfo.innerHTML = `
                <p><strong>ID:</strong> ${user.user_id}</p>
                <p><strong>Username:</strong> ${user.username}</p>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Name:</strong> ${user.first_name} ${user.last_name}</p>
                <p><strong>Role:</strong> ${user.role}</p>
                <p><strong>Created:</strong> ${user.created_at}</p>
            `;

            modal.style.display = 'flex';
            addDebug(`✅ Modal opened for user: ${user.username}`);
        }

        // Delete user
        function deleteUser(userId) {
            addDebug(`🗑️ Delete requested for user ID: ${userId}`);
            
            const user = testUsers.find(u => u.user_id === userId);
            if (!user) {
                addDebug(`❌ User not found with ID: ${userId}`);
                alert('User not found!');
                return;
            }

            if (user.role === 'admin') {
                addDebug(`❌ Cannot delete admin user`);
                alert('Cannot delete admin user!');
                return;
            }

            if (confirm(`Delete user "${user.first_name} ${user.last_name}"?`)) {
                addDebug(`✅ User deletion confirmed: ${user.username}`);
                // In real app, would remove from array and re-render
                alert(`User ${user.username} would be deleted!`);
            } else {
                addDebug(`❌ User deletion cancelled`);
            }
        }

        // Close modal
        function closeModal() {
            document.getElementById('user-modal').style.display = 'none';
            addDebug(`❌ Modal closed`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addDebug('🔧 Test page loaded');
            
            // Render test users
            renderTestUsers();
            
            // Add event listeners
            const tableBody = document.getElementById('test-users-table');
            tableBody.addEventListener('click', handleButtonClick);
            
            // Modal close button
            const closeBtn = document.querySelector('.close-btn');
            closeBtn.addEventListener('click', closeModal);
            
            // Close modal when clicking outside
            window.addEventListener('click', (e) => {
                const modal = document.getElementById('user-modal');
                if (e.target === modal) {
                    closeModal();
                }
            });

            addDebug('✅ Event listeners attached');
        });
    </script>
</body>
</html>
