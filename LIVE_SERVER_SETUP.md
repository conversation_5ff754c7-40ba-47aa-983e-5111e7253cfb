# 🚀 RaoufStore - Live Server Setup Guide

This guide will help you set up and test the RaoufStore e-commerce platform using VS Code Live Server extension.

## 📋 Prerequisites

1. **VS Code** with **Live Server** extension installed
2. **Node.js** (v14 or higher)
3. **PostgreSQL** (v12 or higher)

## 🛠️ Setup Steps

### 1. Database Setup

```sql
-- Create database
CREATE DATABASE ecommerce_phones;

-- Run initialization scripts
psql -U postgres -d ecommerce_phones -f init-db/01-schema.sql
psql -U postgres -d ecommerce_phones -f init-db/02-seed-data.sql
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create .env file with your database credentials
# Copy from .env.example and update values

# Start backend server
npm start
```

The backend will run on `http://localhost:3000`

### 3. Frontend Setup with Live Server

1. **Open VS Code** in the project root directory
2. **Navigate to the frontend folder** in VS Code Explorer
3. **Right-click on `index.html`** in the frontend folder
4. **Select "Open with Live Server"**

The frontend will automatically open in your browser, typically at:
- `http://127.0.0.1:5500/frontend/index.html` or
- `http://localhost:5500/frontend/index.html`

## 🔐 Testing Authentication & Redirection

### Default Admin Credentials
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`

### Test Pages Available

1. **Main Store**: `http://127.0.0.1:5500/frontend/index.html`
2. **Login Page**: `http://127.0.0.1:5500/frontend/login.html`
3. **Admin Dashboard**: `http://127.0.0.1:5500/frontend/admin/admin-dashboard.html`
4. **Auth Test Page**: `http://127.0.0.1:5500/frontend/test-auth.html`

### Authentication Flow Testing

1. **Visit the test page**: `http://127.0.0.1:5500/frontend/test-auth.html`
2. **Check backend connection** - Should show "✅ Connected"
3. **Test admin login**:
   - Go to login page
   - Use admin credentials
   - Should redirect to admin dashboard
4. **Test customer registration**:
   - Register a new account
   - Should redirect to main store

## 🔧 Troubleshooting

### Backend Connection Issues

If you see "❌ Disconnected" in the test page:

1. **Check backend is running**: `http://localhost:3000/api/health`
2. **Verify CORS settings** in `backend/server.js`
3. **Check console for errors** in browser developer tools

### Authentication Issues

1. **Clear browser storage**:
   ```javascript
   // In browser console
   localStorage.clear();
   sessionStorage.clear();
   ```

2. **Check authentication status** on test page
3. **Verify token storage** in browser developer tools

### Live Server Port Issues

If Live Server uses a different port:

1. **Check the actual URL** in your browser
2. **Update CORS settings** in `backend/server.js` if needed
3. **The frontend automatically detects the API URL**

## 📱 Testing Different User Roles

### Admin User Testing
1. Login with admin credentials
2. Should redirect to: `admin/admin-dashboard.html`
3. Can access all admin features
4. Logout redirects to login page

### Customer User Testing
1. Register new account or login as customer
2. Should redirect to: `index.html`
3. Can browse products and add to cart
4. Logout redirects to home page

## 🎯 Key Features to Test

### Authentication Flow
- ✅ Login with admin credentials → Admin dashboard
- ✅ Login with customer credentials → Main store
- ✅ Direct admin access without auth → Login page
- ✅ Logout from any page → Appropriate redirect

### Admin Panel
- ✅ Dashboard with statistics
- ✅ Product management
- ✅ User management
- ✅ Order management
- ✅ Admin user name display

### Customer Features
- ✅ Product browsing
- ✅ Shopping cart
- ✅ User profile
- ✅ Order history

## 🐛 Common Issues & Solutions

### Issue: "CORS Error"
**Solution**: Make sure backend is running and CORS is configured for your Live Server URL

### Issue: "Authentication Failed"
**Solution**: Check if backend database is set up correctly with seed data

### Issue: "Admin Panel Not Loading"
**Solution**: Verify you're logged in as admin user and check browser console for errors

### Issue: "Live Server Wrong Port"
**Solution**: The frontend auto-detects the API URL, but you can manually update it in the JavaScript files if needed

## 📞 Support

If you encounter issues:

1. **Check the test page**: `frontend/test-auth.html`
2. **Review browser console** for error messages
3. **Verify backend logs** for API errors
4. **Check database connection** and data

## 🎉 Success Indicators

You'll know everything is working when:

- ✅ Test page shows "✅ Connected" for backend
- ✅ Admin login redirects to dashboard with your name
- ✅ Customer login redirects to main store
- ✅ Direct admin access is protected
- ✅ Logout works from all pages

Happy testing! 🚀
