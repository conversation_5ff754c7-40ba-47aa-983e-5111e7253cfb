// Admin App Common Functions and Variables
// API URL - Auto-detect environment
const API_URL = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost'
    ? 'http://localhost:3000/api'
    : 'http://localhost:3000/api';
window.API_URL = API_URL;

// Utility function for making API requests with fallback data
window.apiRequest = async function(endpoint, options = {}) {
    try {
        const url = `${API_URL}${endpoint}`;
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        return null; // Return null to indicate the request failed
    }
};

// Format currency
window.formatCurrency = function(amount) {
    return `${parseFloat(amount).toLocaleString()} DZD`;
};

// Format date
window.formatDate = function(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
};

// Show notification
window.showNotification = function(message, type = 'success') {
    // Check if notification container exists, if not create it
    let notificationContainer = document.querySelector('.notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button class="close-notification"><i class="fas fa-times"></i></button>
    `;

    // Add to container
    notificationContainer.appendChild(notification);

    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Auto-hide after 5 seconds
    const hideTimeout = setTimeout(() => {
        hideNotification(notification);
    }, 5000);

    // Close button
    const closeBtn = notification.querySelector('.close-notification');
    closeBtn.addEventListener('click', () => {
        clearTimeout(hideTimeout);
        hideNotification(notification);
    });
};

// Hide notification helper
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Auth functionality
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔧 Admin-app: DOM loaded');

    // Logout button functionality
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('🚪 Admin-app: Logout clicked');

            // Clear all authentication data
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('userRole');
            sessionStorage.removeItem('token');
            sessionStorage.removeItem('user');
            sessionStorage.removeItem('userInfo');
            sessionStorage.removeItem('userRole');

            window.location.href = '../login.html';
        });
    }

    // Check if user is logged in - only for non-login pages
    const isLoginPage = window.location.pathname.endsWith('login.html');
    if (!isLoginPage) {
        console.log('🔐 Admin-app: Checking authentication...');

        // Check for authentication data using the correct keys
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') ||
                        localStorage.getItem('user') || sessionStorage.getItem('user');

        console.log('🔑 Admin-app: Token found:', !!token);
        console.log('👤 Admin-app: User info found:', !!userInfo);

        // If not logged in and not on login page, redirect to login
        if (!token || !userInfo) {
            console.log('❌ Admin-app: No authentication found, redirecting to login');
            window.location.href = '../login.html';
        } else {
            try {
                const userData = JSON.parse(userInfo);
                console.log('👤 Admin-app: User role:', userData.role);

                if (userData.role !== 'admin') {
                    console.log('❌ Admin-app: User is not admin, redirecting to login');
                    window.location.href = '../login.html';
                } else {
                    console.log('✅ Admin-app: Admin authentication verified');
                }
            } catch (error) {
                console.log('❌ Admin-app: Invalid user data, redirecting to login');
                window.location.href = '../login.html';
            }
        }
    } else {
        console.log('📄 Admin-app: On login page, skipping auth check');
    }
});