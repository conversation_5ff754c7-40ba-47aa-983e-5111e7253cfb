<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Redirecting...</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .redirect-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .redirect-content {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        p {
            margin: 0;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="redirect-content">
            <div class="spinner"></div>
            <h1><i class="fas fa-mobile-alt"></i> RaoufStore Admin</h1>
            <p>Redirecting to dashboard...</p>
        </div>
    </div>

    <script>
        // Check authentication and redirect
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || 
                           localStorage.getItem('user') || sessionStorage.getItem('user');

            if (!token || !userInfo) {
                // No authentication, redirect to login
                window.location.href = '../login.html';
                return;
            }

            try {
                const user = JSON.parse(userInfo);
                if (user.role !== 'admin') {
                    // Not an admin, redirect to main site
                    window.location.href = '../index.html';
                    return;
                }

                // Admin user, redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'admin-dashboard.html';
                }, 1000);

            } catch (error) {
                // Invalid user data, redirect to login
                window.location.href = '../login.html';
            }
        });
    </script>
</body>
</html>
