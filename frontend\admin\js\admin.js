document.addEventListener('DOMContentLoaded', () => {
    // Global variables - Auto-detect environment
    window.API_URL = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost'
        ? 'http://localhost:3000/api'
        : 'http://localhost:3000/api';
    window.ADMIN_VALIDATE_URL = `${window.API_URL}/users/validate-admin`;

    // Check authentication on admin pages
    if (!window.location.pathname.includes('login.html')) {
        checkAdminAuthentication();
    }

    // Setup logout functionality
    setupLogoutHandler();

    // Notification system
    window.showNotification = function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            hideNotification(notification);
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            clearTimeout(hideTimeout);
            hideNotification(notification);
        });
    };

    function hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }

    // Check admin authentication
    async function checkAdminAuthentication() {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') ||
                        localStorage.getItem('user') || sessionStorage.getItem('user');

        if (!token || !userInfo) {
            redirectToLogin();
            return;
        }

        try {
            const userData = JSON.parse(userInfo);
            if (userData.role !== 'admin') {
                clearAuthStorage();
                redirectToLogin();
                return;
            }

            // Verify token is still valid by making a test API call
            const response = await fetch(`${window.API_URL}/users/validate-admin`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                clearAuthStorage();
                redirectToLogin();
                return;
            }

            // Mark auth as ready for dependency manager
            if (window.DependencyManager) {
                window.DependencyManager.markReady('auth');
                window.DependencyManager.markReady('api');
            }

        } catch (error) {
            console.error('Authentication check failed:', error);
            clearAuthStorage();
            redirectToLogin();
        }
    }

    // Clear all authentication storage
    function clearAuthStorage() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('userRole');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user');
        sessionStorage.removeItem('userInfo');
        sessionStorage.removeItem('userRole');
    }

    // Redirect to login page
    function redirectToLogin() {
        window.location.href = '../login.html';
    }

    // Setup logout handler
    function setupLogoutHandler() {
        // Add event listener when DOM is ready
        document.addEventListener('click', function(e) {
            if (e.target.id === 'logout-btn' || e.target.closest('#logout-btn')) {
                e.preventDefault();
                handleLogout();
            }
        });
    }

    // Handle logout
    function handleLogout() {
        // Show confirmation dialog
        if (confirm('Are you sure you want to logout?')) {
            // Clear all authentication data
            clearAuthStorage();

            // Show logout message
            if (window.showNotification) {
                window.showNotification('Logged out successfully!', 'success');
            }

            // Redirect to login page after a short delay
            setTimeout(() => {
                redirectToLogin();
            }, 1000);
        }
    }
});

// API utility functions
async function apiRequest(endpoint, method = 'GET', data = null) {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!token) {
        redirectToLogin();
        return null;
    }

    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(`${window.API_URL}${endpoint}`, options);

        if (response.status === 401 || response.status === 403) {
            // Unauthorized or forbidden - redirect to login page
            clearAuthStorage();
            redirectToLogin();
            return null;
        }

        return await response.json();
    } catch (error) {
        if (window.showNotification) {
            window.showNotification('An error occurred while communicating with the server.', 'error');
        }
        throw error; // Re-throw to allow caller to handle
    }
}

// Format utilities
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

function formatCurrency(amount) {
    return parseFloat(amount).toLocaleString() + ' DZD';
}

// Common data loading functions
async function loadProductCount() {
    const productCountElement = document.getElementById('product-count');
    if (!productCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        productCountElement.textContent = stats.productCount || 0;
    }
}

async function loadUserCount() {
    const userCountElement = document.getElementById('user-count');
    if (!userCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        userCountElement.textContent = stats.userCount || 0;
    }
}

async function loadOrderCount() {
    const orderCountElement = document.getElementById('order-count');
    if (!orderCountElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        orderCountElement.textContent = stats.orderCount || 0;
    }
}

async function loadRevenue() {
    const revenueElement = document.getElementById('revenue');
    if (!revenueElement) return;

    const stats = await apiRequest('/admin/stats');
    if (stats) {
        revenueElement.textContent = formatCurrency(stats.totalRevenue || 0);
    }
}