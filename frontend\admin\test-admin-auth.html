<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Auth Test - RaoufStore</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 Admin Authentication Test</h1>
        
        <div class="test-container">
            <h3>Authentication Status</h3>
            <div id="auth-status">Checking...</div>
            <button onclick="checkAuth()">Check Authentication</button>
            <button onclick="goToDashboard()">Go to Dashboard</button>
            <button onclick="goToLogin()">Go to Login</button>
        </div>

        <div class="test-container">
            <h3>Storage Contents</h3>
            <pre id="storage-content"></pre>
            <button onclick="showStorage()">Refresh Storage</button>
        </div>

        <div class="test-container">
            <h3>Console Logs</h3>
            <pre id="console-logs"></pre>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        let logs = [];

        console.log = function(...args) {
            logs.push(`[LOG] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
            originalError.apply(console, args);
        };

        function updateConsoleLogs() {
            document.getElementById('console-logs').textContent = logs.slice(-15).join('\n');
        }

        function checkAuth() {
            console.log('🔐 Test: Checking authentication...');
            
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || 
                           localStorage.getItem('user') || sessionStorage.getItem('user');
            
            console.log('🔑 Test: Token found:', !!token);
            console.log('👤 Test: User info found:', !!userInfo);
            
            const statusDiv = document.getElementById('auth-status');
            
            if (!token || !userInfo) {
                statusDiv.innerHTML = '<div class="status error">❌ Not authenticated</div>';
                console.log('❌ Test: No authentication data found');
                return;
            }

            try {
                const userData = JSON.parse(userInfo);
                console.log('👤 Test: User data:', userData);
                console.log('🔒 Test: User role:', userData.role);
                
                if (userData.role === 'admin') {
                    statusDiv.innerHTML = `<div class="status success">✅ Authenticated as Admin<br>User: ${userData.first_name} ${userData.last_name}<br>Email: ${userData.email}</div>`;
                    console.log('✅ Test: Admin authentication verified');
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ Not an admin user</div>';
                    console.log('❌ Test: User is not admin');
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ Invalid user data</div>';
                console.log('❌ Test: Invalid user data:', error);
            }
        }

        function showStorage() {
            const storage = {
                localStorage: {},
                sessionStorage: {}
            };

            // Get localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage.localStorage[key] = localStorage.getItem(key);
            }

            // Get sessionStorage
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                storage.sessionStorage[key] = sessionStorage.getItem(key);
            }

            document.getElementById('storage-content').textContent = JSON.stringify(storage, null, 2);
        }

        function goToDashboard() {
            console.log('🎯 Test: Navigating to dashboard...');
            window.location.href = 'admin-dashboard.html';
        }

        function goToLogin() {
            console.log('🎯 Test: Navigating to login...');
            window.location.href = '../login.html';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Test: Page loaded');
            checkAuth();
            showStorage();
        });
    </script>
</body>
</html>
