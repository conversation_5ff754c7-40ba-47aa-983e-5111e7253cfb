document.addEventListener('DOMContentLoaded', function() {
    // API URL - Auto-detect environment
    const API_URL = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost'
        ? 'http://localhost:3000/api'
        : 'http://localhost:3000/api';

    // Check if user is already logged in and redirect accordingly
    checkExistingAuth();

    // Tab switching
    const tabs = document.querySelectorAll('.auth-tab');
    const tabContents = document.querySelectorAll('.auth-tab-content');

    // Check URL parameters to open specific tab
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');

    if (tabParam === 'register') {
        // Switch to register tab
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        const registerTab = document.querySelector('[data-tab="register-tab"]');
        const registerContent = document.getElementById('register-tab');

        if (registerTab && registerContent) {
            registerTab.classList.add('active');
            registerContent.classList.add('active');
        }
    }

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.getAttribute('data-tab');

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // Show corresponding content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === targetTab) {
                    content.classList.add('active');
                }
            });
        });
    });

    // Check for existing authentication and redirect accordingly
    function checkExistingAuth() {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        const user = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') ||
                     localStorage.getItem('user') || sessionStorage.getItem('user');

        if (token && user) {
            try {
                const userData = JSON.parse(user);
                redirectBasedOnRole(userData.role);
            } catch (error) {
                // Invalid user data, clear storage
                clearAuthStorage();
            }
        }
    }

    // Clear all authentication storage
    function clearAuthStorage() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('userRole');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user');
        sessionStorage.removeItem('userInfo');
        sessionStorage.removeItem('userRole');
    }

    function redirectBasedOnRole(role) {
        console.log('🔄 Redirecting user with role:', role);

        if (role === 'admin') {
            console.log('👑 Admin user detected, redirecting to admin dashboard');
            const redirectUrl = 'admin/admin-dashboard.html';
            console.log('🎯 Redirect URL:', redirectUrl);
            console.log('🌐 Current URL:', window.location.href);
            console.log('🌐 Base URL:', window.location.origin);

            // Try multiple redirect methods
            try {
                // Method 1: Direct assignment
                window.location.href = redirectUrl;

                // Method 2: Fallback after 2 seconds
                setTimeout(() => {
                    console.log('🔄 Fallback redirect attempt...');
                    window.location.assign(redirectUrl);
                }, 2000);

                // Method 3: Ultimate fallback after 4 seconds
                setTimeout(() => {
                    console.log('🔄 Ultimate fallback redirect...');
                    window.location.replace(redirectUrl);
                }, 4000);

            } catch (error) {
                console.error('❌ Redirect error:', error);
                alert('Redirect failed. Please manually navigate to: ' + redirectUrl);
            }
        } else {
            console.log('👤 Customer user detected, redirecting to home');
            const redirectUrl = 'index.html';
            console.log('🎯 Redirect URL:', redirectUrl);
            window.location.href = redirectUrl;
        }
    }

    // Password visibility toggle
    const toggleButtons = document.querySelectorAll('.toggle-password');

    toggleButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordInput = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('register-password');
    const strengthMeter = document.querySelector('.strength-meter');
    const strengthText = document.querySelector('.strength-text');

    if (passwordInput) {
        passwordInput.addEventListener('input', updatePasswordStrength);
    }

    function updatePasswordStrength() {
        const password = passwordInput.value;
        let strength = 0;

        // Check password length
        if (password.length >= 8) strength += 25;

        // Check for uppercase letters
        if (/[A-Z]/.test(password)) strength += 25;

        // Check for lowercase letters
        if (/[a-z]/.test(password)) strength += 25;

        // Check for numbers or special characters
        if (/[0-9!@#$%^&*]/.test(password)) strength += 25;

        // Update strength meter
        strengthMeter.style.width = `${strength}%`;

        // Update color based on strength
        if (strength < 25) {
            strengthMeter.style.backgroundColor = '#ff4d4d';
            strengthText.textContent = 'Very Weak';
        } else if (strength < 50) {
            strengthMeter.style.backgroundColor = '#ffa64d';
            strengthText.textContent = 'Weak';
        } else if (strength < 75) {
            strengthMeter.style.backgroundColor = '#ffff4d';
            strengthText.textContent = 'Medium';
        } else {
            strengthMeter.style.backgroundColor = '#4CAF50';
            strengthText.textContent = 'Strong';
        }
    }

    // Form submissions
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const remember = document.getElementById('remember-me').checked;

            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Logging in...';
            submitBtn.disabled = true;

            try {
                console.log('🔐 Attempting login with:', { email, password: '***' });
                console.log('🌐 API URL:', `${API_URL}/users/login`);

                // Call the real API for all authentication
                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response ok:', response.ok);

                const data = await response.json();
                console.log('📦 Response data:', data);

                if (response.ok && data.success) {
                    console.log('✅ Login successful:', data);
                    console.log('👤 User data:', data.user);
                    console.log('🔑 User role:', data.user.role);

                    // Store authentication data consistently
                    const storage = remember ? localStorage : sessionStorage;
                    storage.setItem('token', data.token);
                    storage.setItem('userInfo', JSON.stringify(data.user));
                    storage.setItem('userRole', data.user.role);

                    // Also store in localStorage for backward compatibility
                    localStorage.setItem('user', JSON.stringify(data.user));

                    console.log('💾 Authentication data stored');

                    // Use auth manager if available
                    if (window.authManager) {
                        console.log('🔧 Using auth manager');
                        window.authManager.login(data.token, data.user, remember);
                        window.authManager.executePendingAction();
                    }

                    // Show success message
                    showLoginSuccess(`Welcome back, ${data.user.first_name || data.user.username}!`);

                    // Redirect based on user role after a short delay
                    console.log('⏰ Setting redirect timeout...');
                    setTimeout(() => {
                        console.log('🚀 Executing redirect...');
                        redirectBasedOnRole(data.user.role);
                    }, 500);
                } else {
                    throw new Error(data.message || 'Invalid email or password');
                }
            } catch (error) {
                showLoginError(error.message || 'Login failed. Please check your credentials and try again.');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Show login error message
    function showLoginError(message) {
        showMessage(message, 'error', 'login-error');
    }

    // Show login success message
    function showLoginSuccess(message) {
        showMessage(message, 'success', 'login-success');
    }

    // Generic message display function
    function showMessage(message, type, elementId) {
        // Remove existing message
        const existingElement = document.getElementById(elementId);
        if (existingElement) {
            existingElement.remove();
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.id = elementId;
        messageElement.className = `login-message ${type}`;

        const styles = {
            error: {
                backgroundColor: '#f8d7da',
                color: '#721c24',
                border: '1px solid #f5c6cb'
            },
            success: {
                backgroundColor: '#d4edda',
                color: '#155724',
                border: '1px solid #c3e6cb'
            }
        };

        messageElement.style.cssText = `
            background-color: ${styles[type].backgroundColor};
            color: ${styles[type].color};
            border: ${styles[type].border};
            padding: 12px;
            border-radius: 4px;
            margin-top: 15px;
            display: block;
            animation: slideIn 0.3s ease-out;
        `;

        messageElement.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            <span>${message}</span>
        `;

        // Insert it after the login form
        if (loginForm) {
            loginForm.insertAdjacentElement('afterend', messageElement);
        }

        // Hide message after 5 seconds
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    messageElement.remove();
                }, 300);
            }
        }, 5000);
    }

    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const firstName = document.getElementById('register-first-name').value;
            const lastName = document.getElementById('register-last-name').value;
            const email = document.getElementById('register-email').value;
            const phone = document.getElementById('register-phone').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;

            // Validate password match
            if (password !== confirmPassword) {
                alert('Passwords do not match.');
                return;
            }

            // Add user to data manager first (for admin visibility)
            if (window.addUser) {
                const userData = {
                    first_name: firstName,
                    last_name: lastName,
                    email: email,
                    phone: phone,
                    role: 'customer'
                };

                // Add to global data manager
                const newUser = window.addUser(userData);
                }

            // Try server authentication
            try {
                const response = await fetch(`${API_URL}/users/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email,
                        phone_number: phone,
                        password,
                        username: email.split('@')[0] // Generate username from email
                    })
                });

                const data = await response.json();
                if (response.ok && data.success) {
                    // Registration successful
                    alert('Registration successful! Please login with your new account.');

                    // Switch to login tab
                    document.querySelector('[data-tab="login-tab"]').click();

                    // Pre-fill login email
                    document.getElementById('login-email').value = email;
                    return;
                } else {
                    throw new Error(data.message || 'Registration failed');
                }
            } catch (error) {
                // Registration API error

                // Even if server fails, user is already added to data manager
                alert('Registration successful! Please login with your new account.');

                // Switch to login tab
                document.querySelector('[data-tab="login-tab"]').click();

                // Pre-fill login email
                document.getElementById('login-email').value = email;
            }
        });
    }
});