<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Users</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li><a href="admin-dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li class="active"><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Users Management</h1>
                <div class="admin-user">
                    <span id="admin-user-name">Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="content-actions">
                <div class="search-box">
                    <input type="text" id="user-search" placeholder="Search users by name or email...">
                    <button><i class="fas fa-search"></i></button>
                </div>
                <div class="filter-group">
                    <select id="role-filter">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="customer">Customer</option>
                    </select>
                </div>
            </div>
            
            <div class="data-table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Joined Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
                
                <div class="pagination">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button class="pagination-btn" id="next-page" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </main>
        
        <!-- User Details Modal -->
        <div class="modal" id="user-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">User Details</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="user-details">
                        <div class="user-info">
                            <h3>Personal Information</h3>
                            <div class="info-group">
                                <p><strong>Username:</strong> <span id="detail-username"></span></p>
                                <p><strong>Email:</strong> <span id="detail-email"></span></p>
                                <p><strong>First Name:</strong> <span id="detail-first-name"></span></p>
                                <p><strong>Last Name:</strong> <span id="detail-last-name"></span></p>
                                <p><strong>Phone:</strong> <span id="detail-phone"></span></p>
                                <p><strong>Address:</strong> <span id="detail-address"></span></p>
                                <p><strong>Role:</strong> <span id="detail-role"></span></p>
                                <p><strong>Joined:</strong> <span id="detail-joined"></span></p>
                            </div>
                        </div>
                        
                        <div class="user-activity">
                            <h3>User Activity</h3>
                            <div class="activity-stats">
                                <div class="stat">
                                    <span class="stat-value" id="orders-count">0</span>
                                    <span class="stat-label">Orders</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value" id="reviews-count">0</span>
                                    <span class="stat-label">Reviews</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-value" id="total-spent">$0</span>
                                    <span class="stat-label">Total Spent</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recent-orders">
                            <h3>Recent Orders</h3>
                            <table class="detail-table">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Date</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="user-orders-body">
                                    <!-- Recent orders will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/data-manager.js"></script>
    <script src="js/admin-app.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
    <script src="js/users.js"></script>
</body>
</html> 