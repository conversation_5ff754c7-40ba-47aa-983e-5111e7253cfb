<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test - RaoufStore</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .clear-btn { background: #dc3545; }
        .clear-btn:hover { background: #c82333; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .live-server-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 RaoufStore Authentication Test (Live Server)</h1>
        
        <div class="live-server-info">
            <h4>🌐 Live Server Setup</h4>
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Backend API:</strong> <span id="api-url"></span></p>
            <p><strong>Status:</strong> <span id="connection-status">Checking...</span></p>
        </div>
        
        <div class="test-section">
            <h3>Current Authentication Status</h3>
            <div id="auth-status">Checking...</div>
            <button onclick="checkAuthStatus()">Refresh Status</button>
            <button onclick="clearAllAuth()" class="clear-btn">Clear All Auth Data</button>
        </div>

        <div class="test-section">
            <h3>Storage Contents</h3>
            <h4>LocalStorage:</h4>
            <pre id="localStorage-content"></pre>
            <h4>SessionStorage:</h4>
            <pre id="sessionStorage-content"></pre>
        </div>

        <div class="test-section">
            <h3>Test Actions</h3>
            <button onclick="simulateAdminLogin()">Simulate Admin Login</button>
            <button onclick="simulateCustomerLogin()">Simulate Customer Login</button>
            <button onclick="testRedirection()">Test Redirection Logic</button>
            <button onclick="testBackendConnection()">Test Backend Connection</button>
        </div>

        <div class="test-section">
            <h3>Navigation Links</h3>
            <button onclick="goToLogin()">Go to Login Page</button>
            <button onclick="goToHome()">Go to Home Page</button>
            <button onclick="goToAdmin()">Go to Admin Panel</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Auto-detect API URL
        const API_URL = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost' 
            ? 'http://localhost:3000/api' 
            : 'http://localhost:3000/api';

        // Initialize page info
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('api-url').textContent = API_URL;
            checkAuthStatus();
            testBackendConnection();
        });

        // Test backend connection
        async function testBackendConnection() {
            const statusElement = document.getElementById('connection-status');
            try {
                const response = await fetch(`${API_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = '<span class="status success">✅ Connected</span>';
                    addTestResult(`Backend connected: ${data.message}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusElement.innerHTML = '<span class="status error">❌ Disconnected</span>';
                addTestResult(`Backend connection failed: ${error.message}`, 'error');
            }
        }

        // Test functions
        function checkAuthStatus() {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || 
                           localStorage.getItem('user') || sessionStorage.getItem('user');
            
            let status = '<span class="status info">Not authenticated</span>';
            
            if (token && userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    const role = user.role || 'unknown';
                    const name = user.first_name ? `${user.first_name} ${user.last_name}` : user.username;
                    status = `<span class="status success">Authenticated as ${role}</span><br>User: ${name}`;
                } catch (e) {
                    status = '<span class="status error">Invalid user data</span>';
                }
            }
            
            document.getElementById('auth-status').innerHTML = status;
            updateStorageDisplay();
        }

        function updateStorageDisplay() {
            // LocalStorage
            const localStorageData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                localStorageData[key] = localStorage.getItem(key);
            }
            document.getElementById('localStorage-content').textContent = JSON.stringify(localStorageData, null, 2);

            // SessionStorage
            const sessionStorageData = {};
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                sessionStorageData[key] = sessionStorage.getItem(key);
            }
            document.getElementById('sessionStorage-content').textContent = JSON.stringify(sessionStorageData, null, 2);
        }

        function clearAllAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('userRole');
            sessionStorage.removeItem('token');
            sessionStorage.removeItem('user');
            sessionStorage.removeItem('userInfo');
            sessionStorage.removeItem('userRole');
            
            addTestResult('All authentication data cleared', 'info');
            checkAuthStatus();
        }

        function simulateAdminLogin() {
            const adminUser = {
                user_id: 1,
                username: 'admin',
                email: '<EMAIL>',
                first_name: 'Admin',
                last_name: 'User',
                role: 'admin'
            };
            
            localStorage.setItem('token', 'fake-admin-token-' + Date.now());
            localStorage.setItem('userInfo', JSON.stringify(adminUser));
            localStorage.setItem('userRole', 'admin');
            
            addTestResult('Admin login simulated', 'success');
            checkAuthStatus();
        }

        function simulateCustomerLogin() {
            const customerUser = {
                user_id: 2,
                username: 'customer',
                email: '<EMAIL>',
                first_name: 'John',
                last_name: 'Doe',
                role: 'customer'
            };
            
            localStorage.setItem('token', 'fake-customer-token-' + Date.now());
            localStorage.setItem('userInfo', JSON.stringify(customerUser));
            localStorage.setItem('userRole', 'customer');
            
            addTestResult('Customer login simulated', 'success');
            checkAuthStatus();
        }

        function testRedirection() {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || 
                           localStorage.getItem('user') || sessionStorage.getItem('user');
            
            if (!token || !userInfo) {
                addTestResult('No authentication found - would redirect to login.html', 'info');
                return;
            }
            
            try {
                const user = JSON.parse(userInfo);
                if (user.role === 'admin') {
                    addTestResult('Admin user detected - would redirect to admin/admin-dashboard.html', 'success');
                } else {
                    addTestResult('Customer user detected - would redirect to index.html', 'success');
                }
            } catch (e) {
                addTestResult('Invalid user data - would redirect to login.html', 'error');
            }
        }

        function goToLogin() {
            window.location.href = 'login.html';
        }

        function goToHome() {
            window.location.href = 'index.html';
        }

        function goToAdmin() {
            window.location.href = 'admin/admin-dashboard.html';
        }

        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }
    </script>
</body>
</html>
